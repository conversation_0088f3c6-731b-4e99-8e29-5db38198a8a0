# Real Pic Composer 项目结构说明

## 项目文件结构

```
RealPicComposer/
├── RealPicComposer.csproj          # 项目文件
├── App.xaml                        # 应用程序资源定义
├── App.xaml.cs                     # 应用程序入口点
├── MainWindow.xaml                 # 主窗口界面定义
├── MainWindow.xaml.cs              # 主窗口逻辑代码
├── README.md                       # 项目说明文档
├── cmd.md                          # 技术开发文档
├── 项目结构说明.md                  # 本文件
├── Models/                         # 数据模型
│   └── BatchItem.cs               # 批次数据模型
├── Services/                       # 业务服务
│   ├── FolderScanService.cs       # 文件夹扫描服务
│   └── ImageProcessingService.cs  # 图像处理服务
├── Utils/                          # 工具类
│   └── NaturalStringComparer.cs   # 自然排序比较器
└── Windows/                        # 窗口
    ├── PositionSetupWindow.xaml    # 定位设置窗口界面
    └── PositionSetupWindow.xaml.cs # 定位设置窗口逻辑
```

## 核心组件说明

### 1. 主窗口 (MainWindow)
- **MainWindow.xaml**: 定义主界面布局，包含文件夹选择、批次配置、进度显示等控件
- **MainWindow.xaml.cs**: 实现主窗口的业务逻辑，包括事件处理、UI更新、批量处理协调等

### 2. 定位设置窗口 (PositionSetupWindow)
- **PositionSetupWindow.xaml**: 定义交互式定位界面，包含图片预览和标记显示
- **PositionSetupWindow.xaml.cs**: 实现鼠标交互、坐标转换、视觉反馈等功能

### 3. 数据模型 (Models)
- **BatchItem.cs**: 定义批次数据结构，包含路径、背景图、定位点等信息，实现属性变更通知

### 4. 业务服务 (Services)
- **FolderScanService.cs**: 负责文件夹扫描、图片文件识别、批次创建等功能
- **ImageProcessingService.cs**: 负责透视变换、图像合成、文件保存等核心图像处理功能

### 5. 工具类 (Utils)
- **NaturalStringComparer.cs**: 实现自然排序算法，确保文件按正确的数字顺序处理

## 技术实现要点

### 1. 交互式四角定位
- 使用Canvas覆盖在Image控件上捕获鼠标事件
- 实现坐标系转换（控件坐标 ↔ 图片像素坐标）
- 提供实时视觉反馈（彩色标记点和编号）
- 支持窗口大小改变时重新绘制标记

### 2. 透视变换合成
- 使用OpenCvSharp库进行图像处理
- 基于四角点计算透视变换矩阵
- 实现图像变换和掩码合成
- 支持多种图片格式的读取和保存

### 3. 批量处理架构
- 使用异步编程模型避免UI阻塞
- 实现进度回调机制提供实时反馈
- 支持错误处理和异常恢复
- 采用自然排序确保文件处理顺序

### 4. 用户界面设计
- 采用分步骤的操作流程设计
- 使用DataGrid展示和管理批次信息
- 提供实时状态反馈和进度显示
- 支持批次的选择性处理

## 扩展性设计

### 1. 插件化支持
- 服务层设计支持功能扩展
- 图像处理算法可以独立替换
- 支持新的图片格式通过配置添加

### 2. 配置管理
- 支持用户设置的持久化存储
- 可扩展的参数配置系统
- 支持批次配置的保存和加载

### 3. 性能优化
- 支持大批量图片的内存优化处理
- 可配置的并发处理能力
- 支持处理进度的断点续传

## 依赖项说明

### 主要依赖
- **.NET 6.0**: 运行时框架
- **WPF**: 用户界面框架
- **Windows Forms**: 文件夹选择对话框
- **OpenCvSharp4.Windows**: 图像处理库

### 开发依赖
- **Visual Studio 2022** 或 **Visual Studio Code**
- **.NET 6.0 SDK**
- **Windows 10+** 操作系统
