using System.ComponentModel;
using System.Runtime.CompilerServices;
using OpenCvSharp;

namespace RealPicComposer.Models
{
    /// <summary>
    /// 表示一个处理批次的数据模型
    /// </summary>
    public class BatchItem : INotifyPropertyChanged
    {
        private bool _isSelected = true;
        private string _batchPath = string.Empty;
        private string _backgroundPath = string.Empty;
        private Point2f[]? _destinationPoints;
        private bool _isPositionSet;

        /// <summary>
        /// 是否选中该批次进行处理
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 批次文件夹路径
        /// </summary>
        public string BatchPath
        {
            get => _batchPath;
            set
            {
                _batchPath = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 背景图路径
        /// </summary>
        public string BackgroundPath
        {
            get => _backgroundPath;
            set
            {
                _backgroundPath = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanSetPosition));
            }
        }

        /// <summary>
        /// 目标四角坐标点
        /// </summary>
        public Point2f[]? DestinationPoints
        {
            get => _destinationPoints;
            set
            {
                _destinationPoints = value;
                IsPositionSet = value != null && value.Length == 4;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 是否已设置定位点
        /// </summary>
        public bool IsPositionSet
        {
            get => _isPositionSet;
            private set
            {
                _isPositionSet = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(PositionStatus));
            }
        }

        /// <summary>
        /// 定位状态显示文本
        /// </summary>
        public string PositionStatus => IsPositionSet ? "已设置" : "未设置";

        /// <summary>
        /// 是否可以设置定位点（需要先选择背景图）
        /// </summary>
        public bool CanSetPosition => !string.IsNullOrEmpty(BackgroundPath);

        /// <summary>
        /// 批次中的图片文件数量
        /// </summary>
        public int ImageCount { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
