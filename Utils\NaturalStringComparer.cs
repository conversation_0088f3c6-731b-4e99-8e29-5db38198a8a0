using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace RealPicComposer.Utils
{
    /// <summary>
    /// 自然排序比较器，用于正确排序包含数字的文件名
    /// 例如：file1.jpg, file2.jpg, file10.jpg 而不是 file1.jpg, file10.jpg, file2.jpg
    /// </summary>
    public class NaturalStringComparer : IComparer<string>
    {
        private static readonly Regex NumberRegex = new Regex(@"\d+", RegexOptions.Compiled);

        public int Compare(string? x, string? y)
        {
            if (x == null && y == null) return 0;
            if (x == null) return -1;
            if (y == null) return 1;

            var xParts = SplitIntoNaturalParts(x);
            var yParts = SplitIntoNaturalParts(y);

            int minLength = Math.Min(xParts.Count, yParts.Count);

            for (int i = 0; i < minLength; i++)
            {
                var xPart = xParts[i];
                var yPart = yParts[i];

                // 如果两个部分都是数字，按数值比较
                if (int.TryParse(xPart, out int xNum) && int.TryParse(yPart, out int yNum))
                {
                    int numComparison = xNum.CompareTo(yNum);
                    if (numComparison != 0)
                        return numComparison;
                }
                else
                {
                    // 否则按字符串比较
                    int stringComparison = string.Compare(xPart, yPart, StringComparison.OrdinalIgnoreCase);
                    if (stringComparison != 0)
                        return stringComparison;
                }
            }

            // 如果前面的部分都相同，比较长度
            return xParts.Count.CompareTo(yParts.Count);
        }

        private static List<string> SplitIntoNaturalParts(string input)
        {
            var parts = new List<string>();
            var matches = NumberRegex.Matches(input);
            int lastIndex = 0;

            foreach (Match match in matches)
            {
                // 添加数字前的文本部分
                if (match.Index > lastIndex)
                {
                    parts.Add(input.Substring(lastIndex, match.Index - lastIndex));
                }

                // 添加数字部分
                parts.Add(match.Value);
                lastIndex = match.Index + match.Length;
            }

            // 添加最后的文本部分
            if (lastIndex < input.Length)
            {
                parts.Add(input.Substring(lastIndex));
            }

            return parts;
        }
    }
}
