using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using RealPicComposer.Models;
using RealPicComposer.Services;
using RealPicComposer.Windows;

namespace RealPicComposer
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly ImageProcessingService _imageProcessingService;
        private readonly FolderScanService _folderScanService;
        private ObservableCollection<BatchItem> _batchItems;
        private string _sourceFolderPath = string.Empty;
        private string _outputFolderPath = string.Empty;
        private bool _isProcessing;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            _imageProcessingService = new ImageProcessingService();
            _folderScanService = new FolderScanService();
            _batchItems = new ObservableCollection<BatchItem>();
        }

        public ObservableCollection<BatchItem> BatchItems
        {
            get => _batchItems;
            set
            {
                _batchItems = value;
                OnPropertyChanged();
            }
        }

        public bool IsProcessing
        {
            get => _isProcessing;
            set
            {
                _isProcessing = value;
                OnPropertyChanged();
                UpdateUIState();
            }
        }

        private void SelectSourceFolder_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择包含图片的源文件夹",
                ShowNewFolderButton = false
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                _sourceFolderPath = dialog.SelectedPath;
                SourceFolderTextBox.Text = _sourceFolderPath;
                UpdateUIState();
                StatusText.Text = "已选择源文件夹，请选择输出文件夹";
            }
        }

        private void SelectOutputFolder_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择输出文件夹",
                ShowNewFolderButton = true
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                _outputFolderPath = dialog.SelectedPath;
                OutputFolderTextBox.Text = _outputFolderPath;
                UpdateUIState();
                StatusText.Text = "已选择输出文件夹，可以开始扫描源文件夹";
            }
        }

        private async void ScanFolder_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_sourceFolderPath))
                return;

            try
            {
                StatusText.Text = "正在扫描文件夹...";
                ScanButton.IsEnabled = false;

                var batches = await Task.Run(() => _folderScanService.ScanForBatches(_sourceFolderPath));
                
                BatchItems.Clear();
                foreach (var batch in batches)
                {
                    BatchItems.Add(batch);
                }

                StatusText.Text = $"扫描完成，发现 {BatchItems.Count} 个批次";
                UpdateUIState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"扫描文件夹时发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "扫描失败";
            }
            finally
            {
                ScanButton.IsEnabled = true;
            }
        }

        private void SelectBackground_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is BatchItem batchItem)
            {
                var dialog = new OpenFileDialog
                {
                    Title = "选择背景图",
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.bmp;*.tiff|所有文件|*.*",
                    CheckFileExists = true
                };

                if (dialog.ShowDialog() == true)
                {
                    batchItem.BackgroundPath = dialog.FileName;
                    StatusText.Text = $"已为批次 {batchItem.BatchPath} 选择背景图";
                }
            }
        }

        private void SetPosition_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is BatchItem batchItem)
            {
                if (string.IsNullOrEmpty(batchItem.BackgroundPath))
                {
                    MessageBox.Show("请先选择背景图", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var positionWindow = new PositionSetupWindow(batchItem);
                if (positionWindow.ShowDialog() == true)
                {
                    StatusText.Text = $"已为批次 {batchItem.BatchPath} 设置定位点";
                    UpdateUIState();
                }
            }
        }

        private async void StartProcess_Click(object sender, RoutedEventArgs e)
        {
            var selectedBatches = BatchItems.Where(b => b.IsSelected && 
                                                       !string.IsNullOrEmpty(b.BackgroundPath) && 
                                                       b.IsPositionSet).ToList();

            if (!selectedBatches.Any())
            {
                MessageBox.Show("请至少选择一个已配置完成的批次", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            IsProcessing = true;
            
            try
            {
                await ProcessBatchesAsync(selectedBatches);
                MessageBox.Show("处理完成！", "成功", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理过程中发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsProcessing = false;
                ProgressBar.Value = 0;
                ProgressText.Text = "处理完成";
                StatusText.Text = "准备就绪";
            }
        }

        private async Task ProcessBatchesAsync(System.Collections.Generic.List<BatchItem> batches)
        {
            int totalBatches = batches.Count;
            int currentBatch = 0;

            foreach (var batch in batches)
            {
                currentBatch++;
                
                await Dispatcher.InvokeAsync(() =>
                {
                    StatusText.Text = $"正在处理批次 {currentBatch}/{totalBatches}: {Path.GetFileName(batch.BatchPath)}";
                });

                await Task.Run(() => _imageProcessingService.ProcessBatch(batch, _outputFolderPath, 
                    (progress) =>
                    {
                        Dispatcher.InvokeAsync(() =>
                        {
                            double overallProgress = ((currentBatch - 1) * 100 + progress) / totalBatches;
                            ProgressBar.Value = overallProgress;
                            ProgressText.Text = $"{overallProgress:F1}%";
                        });
                    }));
            }
        }

        private void UpdateUIState()
        {
            bool hasSourceAndOutput = !string.IsNullOrEmpty(_sourceFolderPath) && 
                                     !string.IsNullOrEmpty(_outputFolderPath);
            
            ScanButton.IsEnabled = hasSourceAndOutput && !IsProcessing;
            
            bool hasConfiguredBatches = BatchItems.Any(b => b.IsSelected && 
                                                           !string.IsNullOrEmpty(b.BackgroundPath) && 
                                                           b.IsPositionSet);
            
            StartProcessButton.IsEnabled = hasConfiguredBatches && !IsProcessing;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
