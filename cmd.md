# **图片批量合成软件 \- 技术开发文档**

版本: 2.3  
日期: 2025年7月31日  
作者: Gemini

## **1\. 项目概述**

### **1.1 项目目标**

开发一款Windows桌面应用程序，允许用户将一批内容图片，自动、批量地合成到一张指定的背景图上。软件需提供灵活的图形用户界面（GUI），支持复杂的文件夹结构，并能生成具有透视、阴影等真实感的合成效果。

### **1.2 核心功能 (根据新需求更新)**

* **用户友好的图形界面**：提供按钮、列表和预览窗口，引导用户完成操作。  
* **灵活的输入处理**：  
  * 用户可选择一个顶层文件夹作为输入源。  
  * 软件能**自动遍历所有嵌套的子文件夹**，并将每一个包含图片的文件夹识别为一个独立的“处理批次”。  
  * **如果顶层文件夹内没有符合条件的子文件夹，但其自身包含图片，则将该顶层文件夹视为一个单独的批次进行处理。**  
* **按批次指定背景**：用户可以为**每一个识别出的图片批次独立选择不同的背景图**。  
* **交互式四角定位 (新增)**：允许用户在背景图预览上通过鼠标点击来精确指定内容图的四个角的位置，以适应任何背景图。  
* **灵活的输出管理**：  
  * 用户可以统一指定一个输出文件夹来存放所有合成结果。  
  * 合成后的文件名将自动遵循合成真实-{原文件名}的命名规则。  
* **真实感合成**：核心功能，通过\*\*透视变换（四角定位）\*\*技术，将内容图精确嵌入背景的指定区域。  
* **进度反馈**：在处理过程中提供总进度和当前批次的进度，避免程序假死。

## **2\. 技术架构选型**

技术栈保持不变，依然是实现此需求的最佳组合：

* **开发语言**: **C\#**  
* **GUI框架**: **WPF (Windows Presentation Foundation)**  
* **核心图像处理库**: **OpenCvSharp**  

## **3\. 核心功能实现思路**

### **3.1 UI界面设计与交互逻辑**

交互流程升级为更灵活的“配置-执行”模型。

**推荐交互流程**:

1. **第一步：选择源与输出**  
   * 提供 Button (“选择源文件夹”) 和 Button (“选择输出文件夹”)。  
2. **第二步：扫描与批次配置**  
   * 用户选定源文件夹后，软件自动扫描并识别出所有“处理批次”，展示在 DataGrid 控件中。  
   * DataGrid 的每一行代表一个批次，应包含以下列：  
     * \[复选框\]：决定是否处理该批次。  
     * 批次路径 (文本)：显示文件夹路径。  
     * 背景图路径 (文本)：显示为该批次选择的背景图路径。  
     * \[选择背景按钮\]：为该批次选择背景图。  
     * \[设置定位点按钮\]：**新增按钮。当用户选定背景图后，此按钮变为可用。点击后进入交互式选点模式。**  
     * 定位状态 (文本/图标)：显示该批次的定位点是否已设置。  
3. **第三步：交互式定位 (新增流程)**  
   * 用户点击“设置定位点”后，弹出一个专用的窗口或在主界面进入特殊模式。  
   * 预览区域会清晰地展示该批次选择的背景图。  
   * 界面提示用户：“请依次点击目标区域的左上、右上、左下、右下四个角。”  
   * 用户每点击一次，程序就在图上相应位置绘制一个醒目的标记（如带编号的圆圈），并记录坐标。  
   * 完成四点选择后，程序自动保存这组坐标并将其与该批次关联，然后退出选点模式。  
4. **第四步：处理与反馈**  
   * 用户完成所有配置后，点击总的 Button (“开始处理”)。  
   * 界面上提供 ProgressBar 和 Label 来反馈处理进度。

### **3.2 核心算法：基于透视变换的四角定位**

此部分的技术核心保持不变。但关键区别在于：**destPoints (目标坐标) 不再是硬编码的常量，而是从与当前处理批次关联的用户交互数据中动态获取的。**

### **3.3 批量处理与UI线程解耦**

后台处理逻辑需要能访问到每个批次自定义的定位点数据。

**伪代码逻辑**:

// "开始处理" 按钮的点击事件  
private async void StartButton\_Click(object sender, RoutedEventArgs e)  
{  
    // 1\. 从UI的DataGrid中收集所有被勾选且已配置好的批次任务  
    //    每个批次对象(batch)现在包含 SourceFolderPath, BackgroundPath, 和 DestinationPoints  
    var tasksToRun \= GetConfiguredBatchesFromUI();  
    // ...

    await Task.Run(() \=\> {  
        foreach (var batch in tasksToRun)  
        {  
            // 检查该批次是否已设置定位点  
            if (batch.DestinationPoints \== null || batch.DestinationPoints.Length \!= 4\)  
            {  
                // 可以选择跳过或报告错误  
                continue;  
            }

            string\[\] imageFiles \= Directory.GetFiles(batch.SourceFolderPath)  
                                           .Where(f \=\> /\* ... \*/)  
                                           .OrderBy(f \=\> f, new NaturalStringComparer())  
                                           .ToArray();

            for (int i \= 0; i \< imageFiles.Length; i++)  
            {  
                // 调用核心处理函数，传入用户定义的定位点  
                ProcessImageWithPerspective(  
                    imageFiles\[i\],  
                    batch.BackgroundPath,  
                    outputPath,  
                    batch.DestinationPoints // \<-- 使用用户定义的坐标  
                );  
                // ...  
            }  
        }  
    });  
    // ...  
}

### **3.4 文件排序逻辑：自然排序 (Natural Sort)**

此部分保持不变，是确保文件按正确数字顺序处理的关键。实现细节请参考V2.2文档。

### **3.5 交互式定位区域设置 (新增章节)**

这是实现“方式二”的技术核心。

1. **事件监听 (Event Listening)**  
   * 在WPF中，为用于预览背景图的 Image 控件注册 MouseUp 或 MouseDown 事件的监听器。  
2. **坐标转换 (Coordinate Transformation)**  
   * 鼠标事件返回的坐标是相对于 Image 控件本身的。而我们需要的是相对于**图片原始像素**的坐标。这需要进行一次转换，特别是当Image控件的Stretch属性不是None时。  
   * **转换公式**: 图片坐标 \= (鼠标点击坐标 / 控件尺寸) \* 图片原始尺寸。  
   * 必须分别计算X和Y轴的坐标。  
3. **状态管理 (State Management)**  
   * 程序需要一个状态变量（如 bool isInPointSelectionMode）来管理是否处于选点模式。  
   * 需要一个临时的列表（如 List\<Point2f\> currentPoints）来存储用户当前正在选择的四个点。  
   * 当列表中的点达到4个时，自动退出选点模式，并将这4个点保存到DataGrid中对应批次的数据对象里。  
4. **视觉反馈 (Visual Feedback)**  
   * 在WPF中，实现视觉反馈的最佳方式是在Image控件的上层覆盖一个透明的Canvas控件。  
   * 当用户每点击一次，就创建一个新的Ellipse（圆圈）或Path（十字）形状，并将其Canvas.Left和Canvas.Top属性设置为点击的坐标，然后添加到Canvas的子元素中。这样标记就会精确地显示在图片上。

## **4\. 开发工作流**

1. **环境搭建**: 保持不变。  
2. **项目创建**: 保持不变。  
3. **依赖安装**: 保持不变 (OpenCvSharp4.Windows)。  
4. **UI设计**:  
   * 使用XAML设计包含DataGrid的批次配置界面。  
   * 为DataGrid添加“设置定位点”按钮列。  
   * 设计一个用于交互式选点的专用窗口或视图，包含一个大的Image控件和一个覆盖其上的Canvas。  
5. **逻辑编码**:  
   * 实现文件夹扫描逻辑和自然排序逻辑。  
   * **实现交互式四角定位功能**，包括鼠标事件捕获、坐标转换、状态管理和在预览图上绘制视觉反馈。  
   * 将用户选择的定位点数据与DataGrid中的批次项进行绑定。  
   * 重构核心处理循环，使其能使用每个批次自定义的定位点。  
6. **调试与测试**: 重点测试交互式定位的准确性、不同尺寸背景图的坐标转换是否正确。  
7. **打包与发布**: 保持不变。

## **5\. 总结**

V2.3通过引入**交互式四角定位**功能，使软件的通用性和灵活性得到了质的提升。它将一个半自动化的工具转变为一个能适配任意背景、满足更广泛用户需求的强大平台。开发工作的重点，除了原有的文件和批次管理外，新增了对**UI交互、状态管理和坐标系转换**的更高要求。