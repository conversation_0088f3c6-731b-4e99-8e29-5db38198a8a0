using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using RealPicComposer.Models;

namespace RealPicComposer.Services
{
    /// <summary>
    /// 文件夹扫描服务，用于识别包含图片的文件夹作为处理批次
    /// </summary>
    public class FolderScanService
    {
        private static readonly string[] SupportedImageExtensions = 
        {
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"
        };

        /// <summary>
        /// 扫描指定文件夹，识别所有包含图片的文件夹作为处理批次
        /// </summary>
        /// <param name="rootPath">根文件夹路径</param>
        /// <returns>识别出的批次列表</returns>
        public List<BatchItem> ScanForBatches(string rootPath)
        {
            var batches = new List<BatchItem>();

            if (!Directory.Exists(rootPath))
            {
                throw new DirectoryNotFoundException($"指定的文件夹不存在: {rootPath}");
            }

            try
            {
                // 首先检查根文件夹是否包含图片
                var rootImageFiles = GetImageFiles(rootPath);
                
                // 获取所有子文件夹
                var subDirectories = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories)
                                              .OrderBy(d => d, StringComparer.OrdinalIgnoreCase)
                                              .ToList();

                // 如果有子文件夹，遍历每个子文件夹
                if (subDirectories.Any())
                {
                    foreach (var directory in subDirectories)
                    {
                        var imageFiles = GetImageFiles(directory);
                        if (imageFiles.Any())
                        {
                            batches.Add(CreateBatchItem(directory, imageFiles.Count));
                        }
                    }
                }

                // 如果没有找到包含图片的子文件夹，但根文件夹包含图片，则将根文件夹作为一个批次
                if (!batches.Any() && rootImageFiles.Any())
                {
                    batches.Add(CreateBatchItem(rootPath, rootImageFiles.Count));
                }

                return batches;
            }
            catch (UnauthorizedAccessException ex)
            {
                throw new InvalidOperationException($"访问文件夹时权限不足: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"扫描文件夹时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取指定文件夹中的所有图片文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <returns>图片文件路径列表</returns>
        public List<string> GetImageFiles(string folderPath)
        {
            if (!Directory.Exists(folderPath))
            {
                return new List<string>();
            }

            try
            {
                return Directory.GetFiles(folderPath)
                               .Where(file => IsImageFile(file))
                               .OrderBy(file => file, new Utils.NaturalStringComparer())
                               .ToList();
            }
            catch (UnauthorizedAccessException)
            {
                // 如果没有权限访问某个文件夹，跳过它
                return new List<string>();
            }
            catch (Exception)
            {
                // 其他异常也跳过
                return new List<string>();
            }
        }

        /// <summary>
        /// 检查文件是否为支持的图片格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为图片文件</returns>
        public bool IsImageFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return SupportedImageExtensions.Contains(extension);
        }

        /// <summary>
        /// 创建批次项
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="imageCount">图片数量</param>
        /// <returns>批次项</returns>
        private BatchItem CreateBatchItem(string folderPath, int imageCount)
        {
            return new BatchItem
            {
                BatchPath = folderPath,
                ImageCount = imageCount,
                IsSelected = true
            };
        }

        /// <summary>
        /// 验证批次是否有效（包含图片文件）
        /// </summary>
        /// <param name="batchItem">批次项</param>
        /// <returns>是否有效</returns>
        public bool ValidateBatch(BatchItem batchItem)
        {
            if (batchItem == null || string.IsNullOrEmpty(batchItem.BatchPath))
                return false;

            var imageFiles = GetImageFiles(batchItem.BatchPath);
            batchItem.ImageCount = imageFiles.Count;
            
            return imageFiles.Any();
        }

        /// <summary>
        /// 获取支持的图片文件扩展名
        /// </summary>
        /// <returns>支持的扩展名数组</returns>
        public static string[] GetSupportedExtensions()
        {
            return (string[])SupportedImageExtensions.Clone();
        }

        /// <summary>
        /// 获取用于文件对话框的过滤器字符串
        /// </summary>
        /// <returns>过滤器字符串</returns>
        public static string GetImageFileFilter()
        {
            var extensions = string.Join(";", SupportedImageExtensions.Select(ext => $"*{ext}"));
            return $"图片文件|{extensions}|所有文件|*.*";
        }
    }
}
