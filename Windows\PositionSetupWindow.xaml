<Window x:Class="RealPicComposer.Windows.PositionSetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="设置定位点"
        Height="700" Width="900"
        MinHeight="500" MinWidth="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        SizeChanged="Window_SizeChanged">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和说明 -->
        <Border Grid.Row="0" Background="#3498DB" Padding="15">
            <StackPanel>
                <TextBlock Text="交互式四角定位" 
                           FontSize="16" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           HorizontalAlignment="Center"/>
                <TextBlock x:Name="InstructionText" 
                           Text="请依次点击目标区域的左上、右上、右下、左下四个角点" 
                           FontSize="12" 
                           Foreground="White" 
                           HorizontalAlignment="Center"
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- 图片预览区域 -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="2" Margin="10">
            <Grid>
                <!-- 背景图片 -->
                <Image x:Name="BackgroundImage" 
                       Stretch="Uniform" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"/>
                
                <!-- 覆盖的Canvas用于绘制标记点 -->
                <Canvas x:Name="MarkersCanvas" 
                        Background="Transparent"
                        MouseLeftButtonUp="Canvas_MouseLeftButtonUp"/>
            </Grid>
        </Border>

        <!-- 当前选择的点信息 -->
        <Border Grid.Row="2" Background="#ECF0F1" Padding="10" Margin="10,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="左上角" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="Point1Text" Text="未设置" HorizontalAlignment="Center" Margin="0,2"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="右上角" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="Point2Text" Text="未设置" HorizontalAlignment="Center" Margin="0,2"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="右下角" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="Point3Text" Text="未设置" HorizontalAlignment="Center" Margin="0,2"/>
                </StackPanel>

                <StackPanel Grid.Column="3">
                    <TextBlock Text="左下角" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="Point4Text" Text="未设置" HorizontalAlignment="Center" Margin="0,2"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 底部按钮区域 -->
        <Border Grid.Row="3" Background="#34495E" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="重新设置" 
                        x:Name="ResetButton"
                        Click="Reset_Click" 
                        Width="100" 
                        Margin="5"
                        Background="#E74C3C"
                        Foreground="White"/>
                
                <Button Content="确定" 
                        x:Name="ConfirmButton"
                        Click="Confirm_Click" 
                        Width="100" 
                        Margin="5"
                        IsEnabled="False"
                        Background="#27AE60"
                        Foreground="White"/>
                
                <Button Content="取消" 
                        Click="Cancel_Click" 
                        Width="100" 
                        Margin="5"
                        Background="#95A5A6"
                        Foreground="White"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
